@echo off
echo 天玑GPU调速器 - Kotlin Multiplatform版本构建脚本
echo.

:menu
echo 请选择构建目标:
echo 1. Android Debug APK
echo 2. Desktop应用
echo 3. Web (JavaScript)
echo 4. Web (WASM)
echo 5. 清理项目
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto android
if "%choice%"=="2" goto desktop
if "%choice%"=="3" goto web_js
if "%choice%"=="4" goto web_wasm
if "%choice%"=="5" goto clean
if "%choice%"=="6" goto exit
echo 无效选择，请重新输入
goto menu

:android
echo 构建Android Debug APK...
gradlew.bat :composeApp:assembleDebug
if %errorlevel% equ 0 (
    echo Android APK构建成功！
    echo 输出位置: composeApp\build\outputs\apk\debug\
) else (
    echo Android APK构建失败！
)
pause
goto menu

:desktop
echo 运行Desktop应用...
gradlew.bat :composeApp:run
pause
goto menu

:web_js
echo 启动Web (JavaScript) 开发服务器...
gradlew.bat :composeApp:jsBrowserDevelopmentRun
pause
goto menu

:web_wasm
echo 启动Web (WASM) 开发服务器...
gradlew.bat :composeApp:wasmJsBrowserDevelopmentRun
pause
goto menu

:clean
echo 清理项目...
gradlew.bat clean
if %errorlevel% equ 0 (
    echo 项目清理完成！
) else (
    echo 项目清理失败！
)
pause
goto menu

:exit
echo 退出构建脚本
exit /b 0
