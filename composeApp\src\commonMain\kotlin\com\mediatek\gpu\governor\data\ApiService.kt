package com.mediatek.gpu.governor.data

import kotlinx.coroutines.delay
import kotlinx.serialization.json.Json

/**
 * API服务接口，用于与KernelSU WebUI环境交互
 */
interface ApiService {
    suspend fun executeCommand(command: String): CommandResult
    suspend fun showToast(message: String)
    suspend fun readFile(path: String): String
    suspend fun writeFile(path: String, content: String): Boolean
    suspend fun fileExists(path: String): <PERSON>olean
}

/**
 * 命令执行结果
 */
data class CommandResult(
    val errno: Int,
    val stdout: String,
    val stderr: String
) {
    val isSuccess: Boolean get() = errno == 0
}

/**
 * 默认API服务实现
 * 在实际的KernelSU WebUI环境中，这些方法会调用真实的KernelSU API
 * 在其他平台上，这些方法会提供模拟实现
 */
class DefaultApiService : ApiService {
    
    override suspend fun executeCommand(command: String): CommandResult {
        // 在Web环境中，这里会调用KernelSU的exec API
        // 在其他平台上，提供模拟实现
        return try {
            if (isKernelSUWebUI()) {
                executeKernelSUCommand(command)
            } else {
                simulateCommand(command)
            }
        } catch (e: Exception) {
            CommandResult(-1, "", e.message ?: "Unknown error")
        }
    }
    
    override suspend fun showToast(message: String) {
        try {
            if (isKernelSUWebUI()) {
                showKernelSUToast(message)
            } else {
                // 在其他平台上，可以使用日志或其他方式显示消息
                println("Toast: $message")
            }
        } catch (e: Exception) {
            println("Failed to show toast: ${e.message}")
        }
    }
    
    override suspend fun readFile(path: String): String {
        val command = "cat '$path'"
        val result = executeCommand(command)
        return if (result.isSuccess) result.stdout else ""
    }
    
    override suspend fun writeFile(path: String, content: String): Boolean {
        val command = "echo '$content' > '$path'"
        val result = executeCommand(command)
        return result.isSuccess
    }
    
    override suspend fun fileExists(path: String): Boolean {
        val command = "test -f '$path'"
        val result = executeCommand(command)
        return result.isSuccess
    }
    
    private fun isKernelSUWebUI(): Boolean {
        // 检查是否在KernelSU WebUI环境中
        return try {
            js("typeof ksu !== 'undefined'") as Boolean
        } catch (e: Exception) {
            false
        }
    }
    
    private suspend fun executeKernelSUCommand(command: String): CommandResult {
        // 在JavaScript环境中调用KernelSU API
        return try {
            val result = js("""
                new Promise((resolve) => {
                    const callbackName = 'exec_callback_' + Date.now() + '_' + Math.floor(Math.random() * 1000000);
                    window[callbackName] = (errno, stdout, stderr) => {
                        resolve({ errno, stdout, stderr });
                        delete window[callbackName];
                    };
                    ksu.exec(command, JSON.stringify({}), callbackName);
                })
            """) as dynamic
            
            CommandResult(
                errno = result.errno as Int,
                stdout = result.stdout as String,
                stderr = result.stderr as String
            )
        } catch (e: Exception) {
            CommandResult(-1, "", e.message ?: "KernelSU API call failed")
        }
    }
    
    private suspend fun showKernelSUToast(message: String) {
        try {
            js("ksu.toast(message)")
        } catch (e: Exception) {
            println("Failed to show KernelSU toast: ${e.message}")
        }
    }
    
    private suspend fun simulateCommand(command: String): CommandResult {
        // 模拟命令执行，用于非KernelSU环境
        delay(100) // 模拟网络延迟
        
        return when {
            command.contains("cat /data/adb/gpu_governor/log/gpu_gov.log") -> {
                CommandResult(0, "[INFO] GPU调速器已启动\n[INFO] 当前GPU频率: 350MHz\n[INFO] 当前电压: 65000uV\n[DEBUG] 检测到游戏应用启动\n[INFO] 切换到游戏模式\n", "")
            }
            command.contains("cat /data/adb/gpu_governor/log/initsvc.log") -> {
                CommandResult(0, "[INFO] 初始化服务启动\n[INFO] 加载GPU配置文件\n[INFO] 初始化完成\n", "")
            }
            command.contains("cat /data/gpu_freq_table.conf") -> {
                CommandResult(0, "350000 65000 0\n400000 62500 1\n450000 60000 2\n", "")
            }
            command.contains("cat /data/adb/gpu_governor/game/games.conf") -> {
                CommandResult(0, "com.tencent.tmgp.sgame\ncom.miHoYo.GenshinImpact\ncom.tencent.tmgp.pubgmhd\n", "")
            }
            command.contains("test -f") -> {
                CommandResult(0, "", "")
            }
            else -> {
                CommandResult(0, "Command executed successfully", "")
            }
        }
    }
}

/**
 * 全局API服务实例
 */
val apiService: ApiService = DefaultApiService()
