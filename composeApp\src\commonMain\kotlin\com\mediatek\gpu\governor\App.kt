package com.mediatek.gpu.governor

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.*
import androidx.lifecycle.viewmodel.compose.viewModel
import com.mediatek.gpu.governor.ui.MainScreen
import com.mediatek.gpu.governor.ui.theme.AppTheme
import com.mediatek.gpu.governor.viewmodel.AppViewModel

@Composable
fun App() {
    val viewModel: AppViewModel = viewModel { AppViewModel() }
    val isDarkTheme by viewModel.isDarkTheme.collectAsState()
    val followSystemTheme by viewModel.followSystemTheme.collectAsState()
    
    val actualDarkTheme = if (followSystemTheme) {
        isSystemInDarkTheme()
    } else {
        isDarkTheme
    }
    
    AppTheme(darkTheme = actualDarkTheme) {
        MainScreen(viewModel = viewModel)
    }
}
