package com.mediatek.gpu.governor.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.mediatek.gpu.governor.data.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class AppViewModel(
    private val repository: Repository = Repository()
) : ViewModel() {
    // 导航状态
    private val _currentPage = MutableStateFlow(NavigationItem.STATUS)
    val currentPage: StateFlow<NavigationItem> = _currentPage.asStateFlow()

    // 主题设置
    private val _isDarkTheme = MutableStateFlow(false)
    val isDarkTheme: StateFlow<Boolean> = _isDarkTheme.asStateFlow()

    private val _followSystemTheme = MutableStateFlow(true)
    val followSystemTheme: StateFlow<Boolean> = _followSystemTheme.asStateFlow()

    // 系统状态
    private val _systemStatus = MutableStateFlow(
        SystemStatus(
            isRunning = false,
            gameMode = false,
            moduleVersion = "加载中..."
        )
    )
    val systemStatus: StateFlow<SystemStatus> = _systemStatus.asStateFlow()

    // GPU配置
    private val _gpuConfigs = MutableStateFlow<List<GpuConfig>>(emptyList())
    val gpuConfigs: StateFlow<List<GpuConfig>> = _gpuConfigs.asStateFlow()

    // 游戏列表
    private val _gamesList = MutableStateFlow<List<String>>(emptyList())
    val gamesList: StateFlow<List<String>> = _gamesList.asStateFlow()

    // 日志内容
    private val _logContent = MutableStateFlow("加载中...")
    val logContent: StateFlow<String> = _logContent.asStateFlow()

    private val _currentLogType = MutableStateFlow(LogType.MAIN)
    val currentLogType: StateFlow<LogType> = _currentLogType.asStateFlow()

    // 设置
    private val _marginPercentage = MutableStateFlow(20)
    val marginPercentage: StateFlow<Int> = _marginPercentage.asStateFlow()

    private val _language = MutableStateFlow("system")
    val language: StateFlow<String> = _language.asStateFlow()

    private val _logLevel = MutableStateFlow("info")
    val logLevel: StateFlow<String> = _logLevel.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        loadInitialData()
    }

    fun navigateTo(page: NavigationItem) {
        _currentPage.value = page
    }

    fun toggleTheme() {
        _isDarkTheme.value = !_isDarkTheme.value
    }

    fun setFollowSystemTheme(follow: Boolean) {
        _followSystemTheme.value = follow
    }

    fun setLanguage(language: String) {
        _language.value = language
    }

    fun setLogLevel(level: String) {
        _logLevel.value = level
    }

    fun setMarginPercentage(percentage: Int) {
        _marginPercentage.value = percentage
    }

    fun switchLogType(logType: LogType) {
        _currentLogType.value = logType
        loadLogContent()
    }

    fun refreshLog() {
        loadLogContent()
    }

    fun addGpuConfig(config: GpuConfig) {
        val currentList = _gpuConfigs.value.toMutableList()
        currentList.add(config)
        _gpuConfigs.value = currentList
    }

    fun updateGpuConfig(index: Int, config: GpuConfig) {
        val currentList = _gpuConfigs.value.toMutableList()
        if (index in currentList.indices) {
            currentList[index] = config
            _gpuConfigs.value = currentList
        }
    }

    fun removeGpuConfig(index: Int) {
        val currentList = _gpuConfigs.value.toMutableList()
        if (index in currentList.indices) {
            currentList.removeAt(index)
            _gpuConfigs.value = currentList
        }
    }

    fun saveGpuConfigs() {
        viewModelScope.launch {
            val success = repository.saveGpuConfigs(_gpuConfigs.value)
            val message = if (success) "GPU配置保存成功" else "GPU配置保存失败"
            repository.showToast(message)
        }
    }

    fun addGame(packageName: String) {
        val currentList = _gamesList.value.toMutableList()
        if (!currentList.contains(packageName)) {
            currentList.add(packageName)
            _gamesList.value = currentList
        }
    }

    fun removeGame(packageName: String) {
        val currentList = _gamesList.value.toMutableList()
        currentList.remove(packageName)
        _gamesList.value = currentList
    }

    fun saveGamesList() {
        viewModelScope.launch {
            val success = repository.saveGamesList(_gamesList.value)
            val message = if (success) "游戏列表保存成功" else "游戏列表保存失败"
            repository.showToast(message)
        }
    }

    fun saveMarginSettings() {
        viewModelScope.launch {
            val success = repository.saveMarginPercentage(_marginPercentage.value)
            val message = if (success) "余量设置保存成功" else "余量设置保存失败"
            repository.showToast(message)
        }
    }

    private fun loadInitialData() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                loadSystemStatus()
                loadGpuConfigs()
                loadGamesList()
                loadLogContent()
                loadMarginPercentage()
                loadLogLevel()
            } catch (e: Exception) {
                repository.showToast("加载数据失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }

    private suspend fun loadSystemStatus() {
        _systemStatus.value = repository.getSystemStatus()
    }

    private suspend fun loadGpuConfigs() {
        _gpuConfigs.value = repository.getGpuConfigs()
    }

    private suspend fun loadGamesList() {
        _gamesList.value = repository.getGamesList()
    }

    private suspend fun loadLogContent() {
        val logType = _currentLogType.value
        _logContent.value = repository.getLogContent(logType)
    }

    private suspend fun loadMarginPercentage() {
        _marginPercentage.value = repository.getMarginPercentage()
    }

    private suspend fun loadLogLevel() {
        _logLevel.value = repository.getLogLevel()
    }
}
