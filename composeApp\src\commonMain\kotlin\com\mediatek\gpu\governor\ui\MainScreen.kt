package com.mediatek.gpu.governor.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.mediatek.gpu.governor.data.NavigationItem
import com.mediatek.gpu.governor.ui.components.AppIcons
import com.mediatek.gpu.governor.ui.pages.*
import com.mediatek.gpu.governor.viewmodel.AppViewModel
import top.yukonga.miuix.kmp.basic.NavigationBar
import top.yukonga.miuix.kmp.basic.NavigationBarItem
import top.yukonga.miuix.kmp.basic.Scaffold
import top.yukonga.miuix.kmp.basic.Text

@Composable
fun MainScreen(viewModel: AppViewModel) {
    val currentPage by viewModel.currentPage.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    Scaffold(
        bottomBar = {
            NavigationBar {
                NavigationItem.entries.forEach { item ->
                    NavigationBarItem(
                        selected = currentPage == item,
                        onClick = { viewModel.navigateTo(item) },
                        icon = {
                            Icon(
                                imageVector = getIconForNavigationItem(item),
                                contentDescription = item.title
                            )
                        },
                        label = { Text(item.title) }
                    )
                }
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (isLoading) {
                LoadingScreen()
            } else {
                when (currentPage) {
                    NavigationItem.STATUS -> StatusPage(viewModel)
                    NavigationItem.CONFIG -> ConfigPage(viewModel)
                    NavigationItem.LOG -> LogPage(viewModel)
                    NavigationItem.SETTINGS -> SettingsPage(viewModel)
                }
            }
        }
    }
}

@Composable
private fun getIconForNavigationItem(item: NavigationItem): androidx.compose.ui.graphics.vector.ImageVector {
    return when (item) {
        NavigationItem.STATUS -> AppIcons.Dashboard
        NavigationItem.CONFIG -> AppIcons.Settings
        NavigationItem.LOG -> AppIcons.Description
        NavigationItem.SETTINGS -> AppIcons.SettingsApplications
    }
}

@Composable
private fun LoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator()
            Spacer(modifier = Modifier.height(16.dp))
            Text("加载中...")
        }
    }
}
