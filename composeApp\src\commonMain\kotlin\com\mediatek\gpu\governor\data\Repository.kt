package com.mediatek.gpu.governor.data

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 数据仓库，负责管理应用数据和与API的交互
 */
class Repository(private val apiService: ApiService = com.mediatek.gpu.governor.data.apiService) {
    
    // 路径常量
    companion object {
        const val LOG_PATH = "/data/adb/gpu_governor/log"
        const val CONFIG_PATH = "/data/gpu_freq_table.conf"
        const val GAMES_PATH = "/data/adb/gpu_governor/game"
        const val GAMES_FILE = "/data/adb/gpu_governor/game/games.conf"
        const val GAME_MODE_PATH = "/data/adb/gpu_governor/game/game_mode"
        const val LOG_LEVEL_PATH = "/data/adb/gpu_governor/log/log_level"
        const val MARGIN_PATH = "/data/adb/gpu_governor/margin"
    }
    
    // 电压列表
    val voltageList = listOf(
        65000, 64375, 63750, 63125, 62500, 61875, 61250, 60625, 60000,
        59375, 58750, 58125, 57500, 56875, 56250, 55625, 55000, 54375, 53750,
        53125, 52500, 51875, 51250, 50625, 50000, 49375, 48750, 48125, 47500,
        46875, 46250, 45625, 45000, 44375, 43750, 43125, 42500, 41875
    )
    
    /**
     * 获取系统状态
     */
    suspend fun getSystemStatus(): SystemStatus {
        return try {
            val runningResult = apiService.executeCommand("pgrep -f gpu_governor")
            val isRunning = runningResult.isSuccess && runningResult.stdout.isNotBlank()
            
            val gameModeExists = apiService.fileExists(GAME_MODE_PATH)
            
            val versionResult = apiService.executeCommand("cat /data/adb/gpu_governor/version 2>/dev/null || echo 'v1.0.0'")
            val moduleVersion = if (versionResult.isSuccess) versionResult.stdout.trim() else "v1.0.0"
            
            SystemStatus(
                isRunning = isRunning,
                gameMode = gameModeExists,
                moduleVersion = moduleVersion
            )
        } catch (e: Exception) {
            SystemStatus(false, false, "获取失败")
        }
    }
    
    /**
     * 获取GPU配置列表
     */
    suspend fun getGpuConfigs(): List<GpuConfig> {
        return try {
            val result = apiService.readFile(CONFIG_PATH)
            if (result.isBlank()) return emptyList()
            
            result.lines()
                .filter { it.isNotBlank() }
                .mapNotNull { line ->
                    val parts = line.trim().split("\\s+".toRegex())
                    if (parts.size >= 3) {
                        try {
                            GpuConfig(
                                frequency = parts[0].toInt(),
                                voltage = parts[1].toInt(),
                                ddrLevel = parts[2].toInt()
                            )
                        } catch (e: NumberFormatException) {
                            null
                        }
                    } else null
                }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 保存GPU配置列表
     */
    suspend fun saveGpuConfigs(configs: List<GpuConfig>): Boolean {
        return try {
            val content = configs.joinToString("\n") { config ->
                "${config.frequency} ${config.voltage} ${config.ddrLevel}"
            }
            apiService.writeFile(CONFIG_PATH, content)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取游戏列表
     */
    suspend fun getGamesList(): List<String> {
        return try {
            val result = apiService.readFile(GAMES_FILE)
            if (result.isBlank()) return emptyList()
            
            result.lines()
                .filter { it.isNotBlank() }
                .map { it.trim() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * 保存游戏列表
     */
    suspend fun saveGamesList(games: List<String>): Boolean {
        return try {
            // 确保游戏目录存在
            apiService.executeCommand("mkdir -p $GAMES_PATH")
            
            val content = games.joinToString("\n")
            apiService.writeFile(GAMES_FILE, content)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取日志内容
     */
    suspend fun getLogContent(logType: LogType): String {
        return try {
            val logFile = when (logType) {
                LogType.MAIN -> "$LOG_PATH/gpu_gov.log"
                LogType.INIT -> "$LOG_PATH/initsvc.log"
            }
            
            val result = apiService.readFile(logFile)
            if (result.isBlank()) {
                "日志文件为空或不存在"
            } else {
                // 限制日志大小，只显示最后1000行
                val lines = result.lines()
                if (lines.size > 1000) {
                    lines.takeLast(1000).joinToString("\n")
                } else {
                    result
                }
            }
        } catch (e: Exception) {
            "读取日志失败: ${e.message}"
        }
    }
    
    /**
     * 获取余量百分比
     */
    suspend fun getMarginPercentage(): Int {
        return try {
            val result = apiService.readFile(MARGIN_PATH)
            if (result.isBlank()) 20 else result.trim().toIntOrNull() ?: 20
        } catch (e: Exception) {
            20
        }
    }
    
    /**
     * 保存余量百分比
     */
    suspend fun saveMarginPercentage(percentage: Int): Boolean {
        return try {
            apiService.writeFile(MARGIN_PATH, percentage.toString())
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取日志等级
     */
    suspend fun getLogLevel(): String {
        return try {
            val result = apiService.readFile(LOG_LEVEL_PATH)
            if (result.isBlank()) "info" else result.trim()
        } catch (e: Exception) {
            "info"
        }
    }
    
    /**
     * 保存日志等级
     */
    suspend fun saveLogLevel(level: String): Boolean {
        return try {
            apiService.writeFile(LOG_LEVEL_PATH, level)
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 显示Toast消息
     */
    suspend fun showToast(message: String) {
        apiService.showToast(message)
    }
    
    /**
     * 重启GPU调速器服务
     */
    suspend fun restartGpuGovernor(): Boolean {
        return try {
            val result = apiService.executeCommand("killall gpu_governor; /data/adb/gpu_governor/gpu_governor &")
            result.isSuccess
        } catch (e: Exception) {
            false
        }
    }
}
