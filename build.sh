#!/bin/bash

echo "天玑GPU调速器 - Kotlin Multiplatform版本构建脚本"
echo

show_menu() {
    echo "请选择构建目标:"
    echo "1. Android Debug APK"
    echo "2. Desktop应用"
    echo "3. Web (JavaScript)"
    echo "4. Web (WASM)"
    echo "5. 清理项目"
    echo "6. 退出"
    echo
}

while true; do
    show_menu
    read -p "请输入选择 (1-6): " choice
    
    case $choice in
        1)
            echo "构建Android Debug APK..."
            ./gradlew :composeApp:assembleDebug
            if [ $? -eq 0 ]; then
                echo "Android APK构建成功！"
                echo "输出位置: composeApp/build/outputs/apk/debug/"
            else
                echo "Android APK构建失败！"
            fi
            read -p "按Enter键继续..."
            ;;
        2)
            echo "运行Desktop应用..."
            ./gradlew :composeApp:run
            read -p "按Enter键继续..."
            ;;
        3)
            echo "启动Web (JavaScript) 开发服务器..."
            ./gradlew :composeApp:jsBrowserDevelopmentRun
            read -p "按Enter键继续..."
            ;;
        4)
            echo "启动Web (WASM) 开发服务器..."
            ./gradlew :composeApp:wasmJsBrowserDevelopmentRun
            read -p "按Enter键继续..."
            ;;
        5)
            echo "清理项目..."
            ./gradlew clean
            if [ $? -eq 0 ]; then
                echo "项目清理完成！"
            else
                echo "项目清理失败！"
            fi
            read -p "按Enter键继续..."
            ;;
        6)
            echo "退出构建脚本"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
done
