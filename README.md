# 天玑GPU调速器 - Kotlin Multiplatform版本

这是使用 [miuix-kotlin-multiplatform](https://github.com/miuix-kotlin-multiplatform/miuix) 重新实现的天玑GPU调速器WebUI。

## 特性

- 🎨 **现代化UI**: 使用miuix库实现小米HyperOS设计风格
- 🌐 **跨平台支持**: 支持Android、Desktop、Web(JS/WASM)等多个平台
- 🔧 **GPU配置管理**: 管理GPU频率、电压和内存档位配置
- 🎮 **游戏模式**: 支持游戏列表管理和游戏模式切换
- 📊 **实时监控**: 显示模块运行状态和系统信息
- 📝 **日志查看**: 支持主日志和初始化日志查看
- ⚙️ **设置管理**: 主题切换、语言设置、日志等级配置

## 项目结构

```
├── composeApp/                 # 主应用模块
│   ├── src/
│   │   ├── commonMain/        # 共享代码
│   │   │   ├── kotlin/
│   │   │   │   └── com/mediatek/gpu/governor/
│   │   │   │       ├── data/          # 数据模型和API
│   │   │   │       ├── ui/            # UI组件
│   │   │   │       │   ├── components/    # 通用组件
│   │   │   │       │   ├── pages/         # 页面组件
│   │   │   │       │   └── theme/         # 主题配置
│   │   │   │       └── viewmodel/     # ViewModel
│   │   ├── androidMain/       # Android平台代码
│   │   ├── desktopMain/       # 桌面平台代码
│   │   ├── jsMain/           # Web(JS)平台代码
│   │   └── wasmJsMain/       # Web(WASM)平台代码
├── gradle/                    # Gradle配置
└── build.gradle.kts          # 项目构建配置
```

## 技术栈

- **Kotlin Multiplatform**: 跨平台开发框架
- **Compose Multiplatform**: 声明式UI框架
- **miuix**: 小米HyperOS风格UI组件库
- **Kotlinx Coroutines**: 异步编程
- **Kotlinx Serialization**: 数据序列化

## 构建和运行

### 前置要求

- JDK 11 或更高版本
- Android Studio (用于Android开发)
- Xcode (用于iOS开发，macOS环境)

### Android

```bash
./gradlew :composeApp:assembleDebug
```

### Desktop

```bash
./gradlew :composeApp:run
```

### Web (JavaScript)

```bash
./gradlew :composeApp:jsBrowserDevelopmentRun
```

### Web (WASM)

```bash
./gradlew :composeApp:wasmJsBrowserDevelopmentRun
```

## API集成

应用支持与KernelSU WebUI环境集成，在Web环境中会自动检测并使用KernelSU API：

- `ksu.exec()`: 执行shell命令
- `ksu.toast()`: 显示Toast消息

在非KernelSU环境中，会使用模拟数据进行演示。

## 主要功能

### 状态页面
- 显示应用图标和基本信息
- 模块运行状态监控
- 游戏模式状态显示
- 模块版本信息

### 配置页面
- GPU频率表管理
- 电压和内存档位配置
- GPU频率计算余量设置
- 游戏列表管理

### 日志页面
- 主日志查看
- 初始化日志查看
- 日志刷新功能

### 设置页面
- 深色/浅色主题切换
- 跟随系统主题设置
- 多语言支持
- 日志等级配置

## 开发说明

### 添加新页面

1. 在 `ui/pages/` 目录下创建新的Composable函数
2. 在 `NavigationItem` 枚举中添加新的导航项
3. 在 `MainScreen.kt` 中添加路由逻辑

### 添加新的API调用

1. 在 `ApiService` 接口中定义新方法
2. 在 `DefaultApiService` 中实现具体逻辑
3. 在 `Repository` 中添加数据处理逻辑
4. 在 `ViewModel` 中调用Repository方法

## 许可证

本项目基于原WebUI重新实现，遵循相同的开源许可证。

## 致谢

- [miuix-kotlin-multiplatform](https://github.com/miuix-kotlin-multiplatform/miuix) - 提供优秀的UI组件库
- [Compose Multiplatform](https://www.jetbrains.com/compose-multiplatform/) - 跨平台UI框架
- 原WebUI项目 - 提供功能参考和设计灵感
