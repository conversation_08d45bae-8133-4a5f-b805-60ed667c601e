package com.mediatek.gpu.governor.data

import kotlinx.serialization.Serializable

@Serializable
data class GpuConfig(
    val frequency: Int, // 频率 (KHz)
    val voltage: Int,   // 电压 (uV)
    val ddrLevel: Int   // 内存档位
)

@Serializable
data class SystemStatus(
    val isRunning: <PERSON>olean,
    val gameMode: Boolean,
    val moduleVersion: String
)

@Serializable
data class AppSettings(
    val isDarkTheme: Boolean = false,
    val followSystemTheme: Boolean = true,
    val language: String = "system", // "system", "zh", "en"
    val logLevel: String = "info",   // "debug", "info", "warn", "error"
    val marginPercentage: Int = 20
)

enum class NavigationItem(val title: String, val icon: String) {
    STATUS("状态", "status"),
    CONFIG("配置", "config"),
    LOG("日志", "log"),
    SETTINGS("设置", "settings")
}

enum class LogType(val fileName: String, val displayName: String) {
    MAIN("gpu_gov.log", "主日志"),
    INIT("initsvc.log", "初始化日志")
}
