package com.mediatek.gpu.governor.ui.pages

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.Icon
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.mediatek.gpu.governor.ui.components.AppIcons
import com.mediatek.gpu.governor.viewmodel.AppViewModel
import top.yukonga.miuix.kmp.basic.Card
import top.yukonga.miuix.kmp.basic.Text
import top.yukonga.miuix.kmp.basic.SmallTitle

@Composable
fun StatusPage(viewModel: AppViewModel) {
    val systemStatus by viewModel.systemStatus.collectAsState()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // 应用图标卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 应用图标
                    Card(
                        modifier = Modifier.size(64.dp)
                    ) {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = AppIcons.Star,
                                contentDescription = "App Icon",
                                modifier = Modifier.size(32.dp),
                                tint = androidx.compose.material3.MaterialTheme.colorScheme.primary
                            )
                        }
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Column {
                        SmallTitle(text = "天玑GPU调速器")
                        Text(
                            text = "Dimensity GPU Governor",
                            style = androidx.compose.material3.MaterialTheme.typography.bodyMedium,
                            color = androidx.compose.material3.MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }

        item {
            // 模块状态卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    SmallTitle(text = "模块状态")
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    StatusItem(
                        label = "运行状态",
                        value = if (systemStatus.isRunning) "运行中" else "已停止",
                        isPositive = systemStatus.isRunning,
                        icon = if (systemStatus.isRunning) AppIcons.CheckCircle else AppIcons.Cancel
                    )

                    StatusItem(
                        label = "游戏模式",
                        value = if (systemStatus.gameMode) "已启用" else "未启用",
                        isPositive = systemStatus.gameMode,
                        icon = AppIcons.SportsEsports
                    )

                    StatusItem(
                        label = "模块版本",
                        value = systemStatus.moduleVersion,
                        isPositive = true,
                        icon = AppIcons.Info
                    )
                }
            }
        }

        item {
            // 版权信息卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "天玑GPU调速器 © 2025 酷安@瓦力喀 / Github@Seyud"
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusItem(
    label: String,
    value: String,
    isPositive: Boolean,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(16.dp),
                tint = if (isPositive) {
                    androidx.compose.material3.MaterialTheme.colorScheme.primary
                } else {
                    androidx.compose.material3.MaterialTheme.colorScheme.error
                }
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(text = "$label:")
        }

        Text(
            text = value,
            color = if (isPositive) {
                androidx.compose.material3.MaterialTheme.colorScheme.primary
            } else {
                androidx.compose.material3.MaterialTheme.colorScheme.error
            },
            style = androidx.compose.material3.MaterialTheme.typography.bodyMedium
        )
    }
}
