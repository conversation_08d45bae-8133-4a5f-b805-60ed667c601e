package com.mediatek.gpu.governor.ui.components

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 应用图标集合，提供统一的图标访问
 */
object AppIcons {
    // 导航图标
    val Dashboard: ImageVector = Icons.Default.Dashboard
    val Settings: ImageVector = Icons.Default.Settings
    val Description: ImageVector = Icons.Default.Description
    val SettingsApplications: ImageVector = Icons.Default.Settings
    
    // 状态图标
    val CheckCircle: ImageVector = Icons.Default.CheckCircle
    val Cancel: ImageVector = Icons.Default.Cancel
    val SportsEsports: ImageVector = Icons.Default.SportsEsports
    val Info: ImageVector = Icons.Default.Info
    val Star: ImageVector = Icons.Default.Star
    
    // 操作图标
    val Edit: ImageVector = Icons.Default.Edit
    val Delete: ImageVector = Icons.Default.Delete
    val Add: ImageVector = Icons.Default.Add
    val Save: ImageVector = Icons.Default.Save
    val Refresh: ImageVector = Icons.Default.Refresh
    
    // 设置图标
    val Tune: ImageVector = Icons.Default.Tune
    val Language: ImageVector = Icons.Default.Language
    val BugReport: ImageVector = Icons.Default.BugReport
    val DarkMode: ImageVector = Icons.Default.DarkMode
    val LightMode: ImageVector = Icons.Default.LightMode
}
