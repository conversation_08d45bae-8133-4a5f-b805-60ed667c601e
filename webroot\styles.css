:root {
    /* 浅色模式变量 */
    --background-color: #f5f5f5;
    --card-background: #ffffff;
    --text-color: #333333;
    --secondary-text-color: #666666;
    --border-color: #e0e0e0;
    --accent-color: #0d84ff;
    --accent-color-rgb: 13, 132, 255;
    --accent-hover: #0a6edb;
    --accent-bg: rgba(13, 132, 255, 0.1);
    /* 浅色模式下的强调背景色 */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --header-background: #ffffff;
    --header-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    --switch-background: #e0e0e0;
}

[data-theme="dark"] {
    /* 深色模式变量 */
    --background-color: #121212;
    --card-background: #1e1e1e;
    --text-color: #e0e0e0;
    --secondary-text-color: #aaaaaa;
    --border-color: #333333;
    --accent-color: #0d84ff;
    --accent-color-rgb: 13, 132, 255;
    --accent-hover: #3a9bff;
    --accent-bg: rgba(13, 132, 255, 0.2);
    /* 深色模式下的强调背景色 */
    --success-color: #66bb6a;
    --warning-color: #ffa726;
    --error-color: #ef5350;
    --header-background: #1e1e1e;
    --header-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    --card-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    --switch-background: #333333;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 移除默认的焦点轮廓，为自定义样式让路 */
*:focus {
    outline: none;
}

/* 为可点击元素添加统一的焦点样式 */
button:focus,
.card:focus {
    box-shadow: 0 0 0 2px rgba(13, 132, 255, 0.3);
}

/* 主题切换按钮的特殊圆形焦点样式 */
.theme-toggle:focus {
    box-shadow: 0 0 0 2px rgba(13, 132, 255, 0.4);
    background-color: var(--accent-bg);
}

/* 移除底部导航栏按钮的焦点样式 */
.nav-item:focus {
    box-shadow: none;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: background-color 0.3s, color 0.3s;
    padding-bottom: 90px;
    /* 增加底部空间，适应更高的导航栏 */
}

#loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 18px;
    font-weight: 500;
}

#loading::after {
    content: '.';
    animation: ellipsis 1.5s infinite;
}

@keyframes ellipsis {
    0% {
        content: '.';
    }

    33% {
        content: '..';
    }

    66% {
        content: '...';
    }

    100% {
        content: '.';
    }
}

.app-header {
    background-color: var(--header-background);
    box-shadow: var(--header-shadow);
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.header-content {
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--accent-color);
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 1px 2px rgba(13, 132, 255, 0.1);
}

/* 深色模式下Header的特殊效果 */
[data-theme="dark"] .app-header {
    background: linear-gradient(135deg, var(--header-background) 0%, rgba(13, 132, 255, 0.02) 100%);
    border-bottom-color: rgba(13, 132, 255, 0.2);
}

[data-theme="dark"] .header-content h1 {
    text-shadow: 0 2px 4px rgba(13, 132, 255, 0.2);
}

.theme-toggle {
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    outline: none;
    -webkit-tap-highlight-color: transparent;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.theme-toggle:hover {
    background-color: var(--accent-bg);
    transform: scale(1.05);
}

.theme-toggle:active {
    transform: scale(0.95);
    background-color: var(--accent-bg);
    outline: none;
    -webkit-tap-highlight-color: transparent;
}

.theme-toggle svg {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.theme-toggle:hover svg {
    filter: drop-shadow(0 2px 4px rgba(13, 132, 255, 0.3));
}

/* 太阳图标动画效果 */
.light-icon svg circle {
    animation: sunPulse 3s ease-in-out infinite;
    transform-origin: center;
}

.light-icon svg path {
    animation: sunRays 4s linear infinite;
    transform-origin: center;
}

.theme-toggle:hover .light-icon svg circle {
    animation-duration: 1.5s;
}

.theme-toggle:hover .light-icon svg path {
    animation-duration: 2s;
}

/* 月亮图标动画效果 */
.dark-icon svg {
    position: relative;
}

.dark-icon svg path:first-child {
    animation: moonGlow 4s ease-in-out infinite;
}

.dark-icon svg circle {
    animation: crater 6s ease-in-out infinite;
}

.dark-icon svg path:not(:first-child) {
    animation: twinkle 2s ease-in-out infinite;
}

.theme-toggle:hover .dark-icon svg path:first-child {
    animation-duration: 2s;
}

.theme-toggle:hover .dark-icon svg path:not(:first-child) {
    animation-duration: 1s;
}

/* 动画关键帧 */
@keyframes sunPulse {

    0%,
    100% {
        opacity: 0.9;
        transform: scale(1);
    }

    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

@keyframes sunRays {
    0% {
        transform: rotate(0deg);
        opacity: 0.8;
    }

    50% {
        opacity: 1;
    }

    100% {
        transform: rotate(360deg);
        opacity: 0.8;
    }
}

@keyframes moonGlow {

    0%,
    100% {
        opacity: 0.9;
        filter: brightness(1);
    }

    50% {
        opacity: 1;
        filter: brightness(1.1);
    }
}

@keyframes crater {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.5;
        transform: scale(1.05);
    }
}

@keyframes twinkle {

    0%,
    100% {
        opacity: 0.5;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.2);
    }
}

/* 主题图标显示控制 */
.light-icon {
    display: inline-block;
}

.dark-icon {
    display: none;
}

[data-theme="dark"] .light-icon {
    display: none;
}

[data-theme="dark"] .dark-icon {
    display: inline-block;
}

/* 深色模式下的主题切换按钮特殊效果 */
[data-theme="dark"] .theme-toggle {
    background: radial-gradient(circle at center, rgba(13, 132, 255, 0.1) 0%, transparent 70%);
}

[data-theme="dark"] .theme-toggle:hover {
    background: radial-gradient(circle at center, rgba(13, 132, 255, 0.2) 0%, transparent 70%);
    box-shadow: 0 0 12px rgba(13, 132, 255, 0.3);
}

[data-theme="dark"] .theme-toggle svg {
    color: #e0e0e0;
    filter: drop-shadow(0 2px 4px rgba(13, 132, 255, 0.2));
}

[data-theme="dark"] .theme-toggle:hover svg {
    color: #ffffff;
    filter: drop-shadow(0 3px 6px rgba(13, 132, 255, 0.4));
}

/* 主题切换过渡动画 */
.theme-toggle .light-icon,
.theme-toggle .dark-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-toggle .light-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
}

.theme-toggle .dark-icon {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) rotate(-30deg);
}

[data-theme="dark"] .theme-toggle .light-icon {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8) rotate(30deg);
}

[data-theme="dark"] .theme-toggle .dark-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
}

/* 主题切换时的全局过渡效果 */
* {
    transition: background-color 0.3s ease,
        color 0.3s ease,
        border-color 0.3s ease,
        box-shadow 0.3s ease;
}

/* 确保某些元素不受全局过渡影响 */
.theme-toggle svg,
.theme-toggle svg *,
.app-main-icon,
.app-main-icon *,
.nav-icon svg,
.tab-icon svg {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 主题切换按钮的高级交互效果 */
.theme-toggle {
    position: relative;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    transform: scale(1.05);
}

.theme-toggle:active {
    transform: scale(0.95);
}

/* 为主题切换添加音效提示类（可以配合JS使用） */
.theme-toggle.switching::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--accent-color);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    animation: themeSwitch 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    z-index: -1;
}

@keyframes themeSwitch {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.8;
    }

    50% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0.4;
    }

    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

.container {
    max-width: 800px;
    margin: 24px auto;
    padding: 0 16px;
}

.card {
    background-color: var(--card-background);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    margin-bottom: 24px;
    overflow: visible;
    /* 修改为visible，确保弹出内容不会被截断 */
    outline: none;
    /* 移除默认焦点轮廓 */
}

/* 为一般卡片添加焦点样式 */
.card:focus {
    box-shadow: 0 0 0 2px rgba(13, 132, 255, 0.3), var(--card-shadow);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    padding: 18px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-color);
}

/* 为所有卡片标题图标添加miuix风格 */
.card-title .title-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 32px !important;
    height: 32px !important;
    background: linear-gradient(135deg, #0d84ff 0%, #147ce5 100%) !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(13, 132, 255, 0.3) !important;
    color: white !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    flex-shrink: 0 !important;
    margin-right: 12px !important;
}

.card-title .title-icon::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-title .title-icon svg {
    width: 18px !important;
    height: 18px !important;
    color: white !important;
    fill: white !important;
    transition: all 0.3s ease;
    z-index: 1;
    position: relative;
}

.card:hover .card-title .title-icon {
    transform: scale(1.15) rotate(3deg) !important;
    box-shadow: 0 4px 16px rgba(13, 132, 255, 0.4) !important;
}

.card:hover .card-title .title-icon::before {
    opacity: 1 !important;
}

.card:hover .card-title .title-icon svg {
    transform: scale(1.1) !important;
}

/* 深色模式支持 */
[data-theme="dark"] .card-title .title-icon {
    box-shadow: 0 3px 10px rgba(13, 132, 255, 0.4) !important;
}

[data-theme="dark"] .card:hover .card-title .title-icon {
    box-shadow: 0 5px 20px rgba(13, 132, 255, 0.5) !important;
}

/* 移动设备适配 */
@media (max-width: 480px) {
    .card-title .title-icon {
        width: 28px !important;
        height: 28px !important;
        border-radius: 7px !important;
        margin-right: 10px !important;
    }

    .card-title .title-icon svg {
        width: 16px !important;
        height: 16px !important;
    }

    .card:hover .card-title .title-icon {
        transform: scale(1.1) rotate(2deg) !important;
    }
}

/* 动画减少偏好支持 */
@media (prefers-reduced-motion: reduce) {

    .card-title .title-icon,
    .card-title .title-icon svg,
    .card-title .title-icon::before {
        animation: none !important;
        transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    }
}

#statusCard .card-title .title-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    /* 添加miuix风格样式 */
    width: 32px !important;
    height: 32px !important;
    background: linear-gradient(135deg, #0d84ff 0%, #147ce5 100%) !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(13, 132, 255, 0.3) !important;
    color: white !important;
    position: relative !important;
    overflow: hidden !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    flex-shrink: 0 !important;
    margin-right: 12px !important;
}

/* 添加玻璃光泽效果 */
#statusCard .card-title .title-icon::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

#statusCard .card-title .title-icon svg {
    width: 18px !important;
    height: 18px !important;
    color: white !important;
    fill: white !important;
    transition: all 0.3s ease;
    z-index: 1;
    position: relative;
    filter: none;
    /* 移除drop-shadow，因为容器已有阴影 */
}

#statusCard .card:hover .card-title .title-icon {
    transform: scale(1.15) rotate(3deg) !important;
    box-shadow: 0 4px 16px rgba(13, 132, 255, 0.4) !important;
}

#statusCard .card:hover .card-title .title-icon::before {
    opacity: 1 !important;
}

#statusCard .card:hover .card-title .title-icon svg {
    transform: scale(1.1) !important;
}

.card-content {
    padding: 16px;
    overflow: visible;
    /* 确保弹出内容不会被截断 */
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.02);
    border-radius: 12px;
    border: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    position: relative;
    overflow: visible;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-item:hover {
    background-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.05);
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.15);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-item>span:first-child {
    font-weight: 500;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 确保miuix图标容器在文本前正确显示 */
.status-item>span:first-child .miuix-status-icon {
    width: 24px !important;
    height: 24px !important;
    background: linear-gradient(135deg, #0d84ff 0%, #147ce5 100%) !important;
    border-radius: 6px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 2px 6px rgba(13, 132, 255, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
    flex-shrink: 0 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    min-width: 24px !important;
    min-height: 24px !important;
    margin-right: 8px !important;
}

/* 移除有问题的选择器规则，改为针对没有miuix图标的status-item使用默认图标样式 */

/* Miuix风格状态图标容器的伪元素和内部SVG */
.status-item>span:first-child .miuix-status-icon::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.status-item>span:first-child .miuix-status-icon svg.status-icon {
    width: 14px !important;
    height: 14px !important;
    color: white !important;
    fill: white !important;
    opacity: 1 !important;
    z-index: 1 !important;
    position: relative !important;
}

/* 悬停效果 */
.status-item:hover .miuix-status-icon {
    transform: scale(1.1) rotate(2deg) !important;
    box-shadow: 0 4px 12px rgba(13, 132, 255, 0.4) !important;
}

.status-item:hover .miuix-status-icon::before {
    opacity: 1 !important;
}

.status-item:hover .miuix-status-icon svg.status-icon {
    transform: scale(1.05) !important;
}

/* GPU状态图标特殊动画效果 */
.status-item>span:first-child .miuix-status-icon svg.status-icon path:first-child {
    animation: statusIconPulse 3s ease-in-out infinite;
}

.status-item>span:first-child .miuix-status-icon svg.status-icon circle {
    animation: statusIconGlow 2s ease-in-out infinite alternate;
}

@keyframes statusIconPulse {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.5;
        transform: scale(1.02);
    }
}

@keyframes statusIconGlow {
    0% {
        opacity: 0.7;
        transform: scale(0.95);
    }

    100% {
        opacity: 1;
        transform: scale(1.05);
    }
}

/* 深色模式下的特殊效果 */
[data-theme="dark"] .status-item>span:first-child .miuix-status-icon {
    box-shadow: 0 3px 8px rgba(13, 132, 255, 0.4) !important;
}

[data-theme="dark"] .status-item:hover .miuix-status-icon {
    box-shadow: 0 6px 16px rgba(13, 132, 255, 0.5) !important;
}

/* 移动设备响应式适配 */
@media (max-width: 480px) {
    .status-item>span:first-child .miuix-status-icon {
        width: 20px !important;
        height: 20px !important;
        border-radius: 5px !important;
        min-width: 20px !important;
        min-height: 20px !important;
    }

    .status-item>span:first-child .miuix-status-icon svg.status-icon {
        width: 12px !important;
        height: 12px !important;
    }

    .status-item:hover .miuix-status-icon {
        transform: scale(1.05) !important;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {

    .status-item>span:first-child .miuix-status-icon,
    .status-item>span:first-child .miuix-status-icon svg.status-icon,
    .status-item>span:first-child .miuix-status-icon svg.status-icon * {
        animation: none !important;
        transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    }
}

.status-item>span:first-child::before {
    display: none;
    /* 隐藏原来的圆点，因为现在有图标了 */
}

.status-item:hover>span:first-child::before {
    display: none;
}

/* 深色模式下的状态项样式调整 */
[data-theme="dark"] .status-item {
    background-color: rgba(13, 132, 255, 0.05);
    border-color: rgba(13, 132, 255, 0.15);
}

[data-theme="dark"] .status-item:hover {
    background-color: rgba(13, 132, 255, 0.08);
    border-color: rgba(13, 132, 255, 0.25);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Miuix风格卡片特殊样式 - 应用到所有主要卡片 */
#statusCard,
#gpuConfigCard,
#marginCard,
#gamesCard,
#logCard,
#settingsCard,
#appIconCard,
#copyrightCard {
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    border: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#statusCard::before,
#gpuConfigCard::before,
#marginCard::before,
#gamesCard::before,
#logCard::before,
#settingsCard::before,
#appIconCard::before,
#copyrightCard::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(var(--accent-color-rgb, 13, 132, 255), 0.03) 0%, transparent 70%);
    pointer-events: none;
}

#statusCard:hover,
#gpuConfigCard:hover,
#marginCard:hover,
#gamesCard:hover,
#logCard:hover,
#settingsCard:hover,
#appIconCard:hover,
#copyrightCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.15);
}

#statusCard .card-title,
#gpuConfigCard .card-title,
#marginCard .card-title,
#gamesCard .card-title,
#logCard .card-title,
#settingsCard .card-title {
    background: linear-gradient(135deg, transparent 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.05) 100%);
    border-bottom: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.1);
    position: relative;
}

#statusCard .card-title::after,
#gpuConfigCard .card-title::after,
#marginCard .card-title::after,
#gamesCard .card-title::after,
#logCard .card-title::after,
#settingsCard .card-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color) 0%, transparent 100%);
    opacity: 0.3;
}

#statusCard .card-content,
#gpuConfigCard .card-content,
#marginCard .card-content,
#gamesCard .card-content,
#logCard .card-content,
#settingsCard .card-content {
    position: relative;
    z-index: 1;
    padding: 20px;
}

/* 深色模式下的所有卡片样式 */
[data-theme="dark"] #statusCard,
[data-theme="dark"] #gpuConfigCard,
[data-theme="dark"] #marginCard,
[data-theme="dark"] #gamesCard,
[data-theme="dark"] #logCard,
[data-theme="dark"] #settingsCard,
[data-theme="dark"] #appIconCard,
[data-theme="dark"] #copyrightCard {
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.15);
}

[data-theme="dark"] #statusCard::before,
[data-theme="dark"] #gpuConfigCard::before,
[data-theme="dark"] #marginCard::before,
[data-theme="dark"] #gamesCard::before,
[data-theme="dark"] #logCard::before,
[data-theme="dark"] #settingsCard::before,
[data-theme="dark"] #appIconCard::before,
[data-theme="dark"] #copyrightCard::before {
    background: radial-gradient(circle at top right, rgba(13, 132, 255, 0.08) 0%, transparent 70%);
}

[data-theme="dark"] #statusCard:hover,
[data-theme="dark"] #gpuConfigCard:hover,
[data-theme="dark"] #marginCard:hover,
[data-theme="dark"] #gamesCard:hover,
[data-theme="dark"] #logCard:hover,
[data-theme="dark"] #settingsCard:hover,
[data-theme="dark"] #appIconCard:hover,
[data-theme="dark"] #copyrightCard:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    border-color: rgba(13, 132, 255, 0.25);
}

[data-theme="dark"] #statusCard .card-title,
[data-theme="dark"] #gpuConfigCard .card-title,
[data-theme="dark"] #marginCard .card-title,
[data-theme="dark"] #gamesCard .card-title,
[data-theme="dark"] #logCard .card-title,
[data-theme="dark"] #settingsCard .card-title {
    background: linear-gradient(135deg, transparent 0%, rgba(13, 132, 255, 0.08) 100%);
    border-bottom-color: rgba(13, 132, 255, 0.2);
}

/* 设置页面中的选项间距更大 */
#settingsCard .status-item {
    margin-bottom: 24px;
    /* 增加间距 */
    padding: 16px 20px;
    /* 增加内边距 */
    background-color: transparent;
    border: none;
    border-radius: 8px;
}

#settingsCard .status-item:hover {
    background-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.03);
    transform: none;
    box-shadow: none;
}

.status-item:last-child {
    margin-bottom: 0;
}

.setting-description {
    margin-top: 8px;
    margin-bottom: 16px;
    /* 添加底部间距 */
    color: var(--secondary-text-color);
    font-size: 12px;
}

/* 设置页面中的描述文本 */
#settingsCard .setting-description {
    margin-bottom: 24px;
    /* 增加底部间距 */
}

.select-container {
    min-width: 150px;
    position: relative;
    /* 确保子元素的绝对定位是相对于它 */
    overflow: visible;
    /* 确保弹出内容不会被截断 */
}

/* Miuix风格状态徽章 */
.status-badge {
    padding: 6px 16px 6px 20px;
    /* 左侧增加padding为红点留出空间 */
    border-radius: 16px;
    font-size: 13px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    /* 启用硬件加速 */
}

.status-badge::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.status-badge:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.status-badge:hover::before {
    opacity: 1;
}

.status-badge::after {
    content: "";
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    left: 8px;
    /* 红点位置保持在左侧边缘 */
    top: 50%;
    transform: translateY(-50%);
}

.status-running {
    background: linear-gradient(135deg, var(--success-color) 0%, #66bb6a 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.status-running:hover {
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.4);
}

.status-running::after {
    background-color: rgba(255, 255, 255, 0.8);
    animation: statusPulse 2s ease-in-out infinite;
}

.status-stopped {
    background: linear-gradient(135deg, var(--error-color) 0%, #ef5350 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.3);
}

.status-stopped:hover {
    box-shadow: 0 4px 16px rgba(244, 67, 54, 0.4);
}

.status-stopped::after {
    background-color: rgba(255, 255, 255, 0.6);
    animation: none;
}

.version-badge {
    padding: 6px 16px;
    /* 移除左侧额外padding，因为不需要v图标了 */
    border-radius: 16px;
    font-size: 13px;
    font-weight: 600;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    color: white;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 8px rgba(13, 132, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.version-badge::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.version-badge:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow: 0 4px 16px rgba(13, 132, 255, 0.4);
}

.version-badge:hover::before {
    opacity: 1;
}

/* 移除version-badge的::after伪元素，因为版本号本身已经包含v */

@keyframes statusPulse {

    0%,
    100% {
        opacity: 0.8;
        transform: translateY(-50%) scale(1);
    }

    50% {
        opacity: 1;
        transform: translateY(-50%) scale(1.2);
        box-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
    }
}

/* 添加状态切换时的动画效果 */
.status-badge.status-changing {
    animation: statusChange 0.6s ease-in-out;
}

@keyframes statusChange {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--switch-background);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
}

input:checked+.slider {
    background-color: var(--accent-color);
}

input:focus+.slider {
    box-shadow: 0 0 1px var(--accent-color);
}

input:checked+.slider:before {
    transform: translateX(24px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Miuix风格表格样式 */
.table-container {
    overflow-x: auto;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    border: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
}

th,
td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    transition: background-color 0.3s ease;
}

th {
    font-weight: 600;
    color: var(--text-color);
    background: linear-gradient(135deg, transparent 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.05) 100%);
    font-size: 14px;
}

tbody tr {
    transition: all 0.3s ease;
}

tbody tr:hover {
    background: rgba(var(--accent-color-rgb, 13, 132, 255), 0.05);
    transform: translateY(-1px);
}

tbody tr:last-child td {
    border-bottom: none;
}

/* 深色模式表格样式 */
[data-theme="dark"] .table-container {
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.15);
}

[data-theme="dark"] th,
[data-theme="dark"] td {
    border-bottom-color: rgba(13, 132, 255, 0.15);
}

[data-theme="dark"] th {
    background: linear-gradient(135deg, transparent 0%, rgba(13, 132, 255, 0.08) 100%);
}

[data-theme="dark"] tbody tr:hover {
    background: rgba(13, 132, 255, 0.08);
}

/* Miuix风格游戏列表样式 */
.games-list {
    list-style: none;
    max-height: 300px;
    overflow-y: auto;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    border: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    border-radius: 12px;
    padding: 8px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.games-list li {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    border-radius: 8px;
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    background: transparent;
}

.games-list li:hover {
    background: rgba(var(--accent-color-rgb, 13, 132, 255), 0.05);
    transform: translateX(4px);
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.15);
}

.games-list li:last-child {
    border-bottom: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    margin-bottom: 0;
}

.games-actions {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin-top: 16px;
}

/* Miuix风格删除按钮 */
.game-delete-btn {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(244, 67, 54, 0.1) 100%);
    border: 1px solid rgba(231, 76, 60, 0.2);
    border-radius: 8px;
    cursor: pointer;
    color: var(--error-color);
    padding: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.1);
}

.game-delete-btn svg {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
}

.game-delete-btn:hover {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.15) 0%, rgba(244, 67, 54, 0.15) 100%);
    border-color: rgba(231, 76, 60, 0.4);
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.2);
}

.game-delete-btn:hover svg {
    transform: scale(1.1) rotate(5deg);
}

/* 深色模式游戏列表样式 */
[data-theme="dark"] .games-list {
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.15);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .games-list li {
    border-bottom-color: rgba(13, 132, 255, 0.15);
}

[data-theme="dark"] .games-list li:hover {
    background: rgba(13, 132, 255, 0.08);
    border-color: rgba(13, 132, 255, 0.25);
}

[data-theme="dark"] .games-list li:last-child {
    border-bottom-color: rgba(13, 132, 255, 0.15);
}

/* 日志样式 */
.log-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
    gap: 16px;
}

.log-tabs-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    flex: 1;
    max-width: 300px;
}

/* Miuix风格日志标签按钮 */
.log-tab-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 12px;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    border: 2px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--text-color);
    min-height: 80px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.log-tab-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.log-tab-btn:hover {
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.08) 100%);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.log-tab-btn:hover::before {
    opacity: 1;
}

.log-tab-btn.active {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    border-color: var(--accent-color);
    color: white;
    box-shadow: 0 6px 20px rgba(var(--accent-color-rgb, 13, 132, 255), 0.4);
    transform: translateY(-2px);
}

.log-tab-btn.active::before {
    opacity: 1;
}

.log-tab-btn .tab-icon {
    font-size: 20px;
    margin-bottom: 4px;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.log-tab-btn .tab-icon svg {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.log-tab-btn .tab-text {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

.log-tab-btn:hover .tab-icon {
    transform: scale(1.1);
}

.log-tab-btn:hover .tab-icon svg {
    transform: scale(1.1);
}

.log-tab-btn.active .tab-icon {
    animation: pulse 2s infinite;
}

.log-tab-btn.active .tab-icon svg {
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Miuix风格刷新按钮 */
.refresh-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    white-space: nowrap;
    flex-shrink: 0;
    background: linear-gradient(135deg, rgba(var(--accent-color-rgb, 13, 132, 255), 0.1) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.05) 100%);
    border: 2px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.2);
    border-radius: 12px;
    color: var(--accent-color);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(var(--accent-color-rgb, 13, 132, 255), 0.1);
    position: relative;
    overflow: hidden;
}

.refresh-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.refresh-btn:hover {
    background: linear-gradient(135deg, rgba(var(--accent-color-rgb, 13, 132, 255), 0.15) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.08) 100%);
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.4);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(var(--accent-color-rgb, 13, 132, 255), 0.2);
}

.refresh-btn:hover::before {
    opacity: 1;
}

.refresh-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px rgba(var(--accent-color-rgb, 13, 132, 255), 0.2);
}

.refresh-icon {
    font-size: 16px;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-icon svg {
    width: 16px;
    height: 16px;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.refresh-btn:hover .refresh-icon {
    transform: rotate(360deg) scale(1.1);
}

.refresh-btn:hover .refresh-icon svg {
    transform: rotate(360deg) scale(1.1);
}

/* 深色模式刷新按钮 */
[data-theme="dark"] .refresh-btn {
    background: linear-gradient(135deg, rgba(13, 132, 255, 0.15) 0%, rgba(13, 132, 255, 0.08) 100%);
    border-color: rgba(13, 132, 255, 0.3);
}

[data-theme="dark"] .refresh-btn:hover {
    background: linear-gradient(135deg, rgba(13, 132, 255, 0.2) 0%, rgba(13, 132, 255, 0.12) 100%);
    border-color: rgba(13, 132, 255, 0.5);
}

/* SVG图标通用样式 */
svg {
    vertical-align: middle;
    color: inherit;
    fill: currentColor;
}

/* 确保SVG图标在不同主题下都能正确显示 */
.theme-toggle svg,
.nav-icon svg,
.tab-icon svg,
.refresh-icon svg,
.edit-btn svg,
.delete-btn svg,
.game-delete-btn svg {
    color: inherit;
    fill: currentColor;
}

/* 提升SVG图标的交互体验 */
button:hover svg {
    transition: transform 0.2s ease;
}

/* Miuix风格应用图标样式 */
#appIconCard {
    margin-bottom: 32px;
    background: linear-gradient(135deg, var(--accent-bg) 0%, transparent 100%);
    border: 1px solid var(--accent-color);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    /* 移除默认的焦点轮廓 */
    outline: none;
    /* 确保可访问性的焦点指示 */
    -webkit-tap-highlight-color: transparent;
}

/* 自定义焦点样式，匹配圆角矩形设计 */
#appIconCard:focus {
    box-shadow: 0 0 0 3px rgba(13, 132, 255, 0.3), 0 12px 32px rgba(13, 132, 255, 0.15);
    border-color: var(--accent-hover);
}

/* 鼠标和焦点状态的组合 */
#appIconCard:focus:hover {
    box-shadow: 0 0 0 3px rgba(13, 132, 255, 0.4), 0 12px 32px rgba(13, 132, 255, 0.2);
}

#appIconCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(13, 132, 255, 0.15);
    border-color: var(--accent-hover);
}

#appIconCard::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(13, 132, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    transition: opacity 0.3s ease;
}

#appIconCard:hover::before {
    opacity: 1.5;
}

.app-icon-content {
    padding: 24px 20px !important;
    position: relative;
    z-index: 1;
}

.miuix-app-icon {
    display: flex;
    align-items: center;
    gap: 16px;
}

.icon-background {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 20px rgba(13, 132, 255, 0.3);
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#appIconCard:hover .icon-background {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 12px 28px rgba(13, 132, 255, 0.4);
}

.icon-background::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 16px;
    transition: opacity 0.3s ease;
}

#appIconCard:hover .icon-background::before {
    opacity: 0.7;
}

.app-main-icon {
    color: white;
    width: 40px;
    height: 40px;
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#appIconCard:hover .app-main-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.app-info {
    flex: 1;
    min-width: 0;
}

.app-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 4px 0;
    line-height: 1.2;
    transition: color 0.3s ease;
}

#appIconCard:hover .app-title {
    color: var(--accent-color);
}

.app-subtitle {
    font-size: 14px;
    color: var(--secondary-text-color);
    margin: 0;
    opacity: 0.8;
    font-weight: 400;
    transition: opacity 0.3s ease;
}

#appIconCard:hover .app-subtitle {
    opacity: 1;
}

/* 添加微动画效果 */
@keyframes breathe {

    0%,
    100% {
        box-shadow: 0 8px 20px rgba(13, 132, 255, 0.3);
    }

    50% {
        box-shadow: 0 8px 20px rgba(13, 132, 255, 0.4);
    }
}

.icon-background {
    animation: breathe 4s ease-in-out infinite;
}

/* 深色模式下的特殊效果 */
[data-theme="dark"] #appIconCard {
    background: linear-gradient(135deg, rgba(13, 132, 255, 0.15) 0%, transparent 100%);
    border-color: rgba(13, 132, 255, 0.6);
}

[data-theme="dark"] #appIconCard:hover {
    box-shadow: 0 12px 32px rgba(13, 132, 255, 0.25);
}

[data-theme="dark"] #appIconCard:focus {
    box-shadow: 0 0 0 3px rgba(13, 132, 255, 0.4), 0 12px 32px rgba(13, 132, 255, 0.25);
    border-color: var(--accent-color);
}

[data-theme="dark"] #appIconCard:focus:hover {
    box-shadow: 0 0 0 3px rgba(13, 132, 255, 0.5), 0 12px 32px rgba(13, 132, 255, 0.3);
}

[data-theme="dark"] .icon-background {
    box-shadow: 0 8px 20px rgba(13, 132, 255, 0.4);
}

[data-theme="dark"] #appIconCard:hover .icon-background {
    box-shadow: 0 12px 28px rgba(13, 132, 255, 0.5);
}

/* GPU主题图标特殊效果 */
.app-main-icon path:first-child {
    animation: starPulse 3s ease-in-out infinite;
}

.app-main-icon circle {
    animation: corePulse 2s ease-in-out infinite alternate;
}

.app-main-icon path:last-child {
    animation: crossFade 4s ease-in-out infinite;
}

@keyframes starPulse {

    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.5;
        transform: scale(1.05);
    }
}

@keyframes corePulse {
    0% {
        opacity: 0.6;
        transform: scale(0.9);
    }

    100% {
        opacity: 1;
        transform: scale(1.1);
    }
}

@keyframes crossFade {

    0%,
    100% {
        opacity: 0.4;
    }

    50% {
        opacity: 0.8;
    }
}

#appIconCard:hover .app-main-icon path:first-child {
    animation-duration: 1.5s;
}

#appIconCard:hover .app-main-icon circle {
    animation-duration: 1s;
}

#appIconCard:hover .app-main-icon path:last-child {
    animation-duration: 2s;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .app-icon-content {
        padding: 20px 16px !important;
    }

    .miuix-app-icon {
        gap: 12px;
    }

    .icon-background {
        width: 56px;
        height: 56px;
        border-radius: 14px;
    }

    .app-main-icon {
        width: 32px;
        height: 32px;
    }

    .app-title {
        font-size: 20px;
    }

    .app-subtitle {
        font-size: 13px;
    }

    #appIconCard:hover {
        transform: translateY(-1px);
    }
}

/* 移动设备和触摸优化 */
@media (max-width: 480px) {
    .theme-toggle {
        width: 44px;
        height: 44px;
        padding: 8px;
        /* 增加触摸区域 */
        min-width: 44px;
        min-height: 44px;
        border-radius: 50%;
        -webkit-tap-highlight-color: transparent;
    }

    .theme-toggle svg {
        width: 16px;
        height: 16px;
    }

    /* 减少动画强度以提升性能 */
    .theme-toggle .light-icon svg circle {
        animation: none;
    }

    .theme-toggle .light-icon svg path {
        animation: sunRays 6s linear infinite;
    }

    .theme-toggle .dark-icon svg path:first-child {
        animation: moonGlow 6s ease-in-out infinite;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .theme-toggle {
        border: 2px solid var(--text-color);
        border-radius: 50%;
        -webkit-tap-highlight-color: transparent;
    }

    .theme-toggle svg {
        filter: none;
    }

    .theme-toggle:hover {
        background-color: var(--text-color);
    }

    .theme-toggle:hover svg {
        color: var(--background-color);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {

    .theme-toggle,
    .theme-toggle svg,
    .theme-toggle svg *,
    .theme-toggle .light-icon,
    .theme-toggle .dark-icon {
        animation: none !important;
        transition: opacity 0.2s ease, transform 0.2s ease !important;
    }

    .theme-toggle {
        border-radius: 50%;
        -webkit-tap-highlight-color: transparent;
    }

    .theme-toggle::before,
    .theme-toggle::after {
        display: none !important;
    }
}

/* 确保主题切换按钮在各种背景下都清晰可见 */
.theme-toggle {
    isolation: isolate;
}

/* 添加细微的边框以增强定义 */
[data-theme="light"] .theme-toggle {
    border: 1px solid rgba(0, 0, 0, 0.08);
}

[data-theme="dark"] .theme-toggle {
    border: 1px solid rgba(255, 255, 255, 0.12);
}

/* 彻底移除主题切换按钮的默认选中效果 */
.theme-toggle::selection {
    background: transparent;
}

.theme-toggle::-moz-selection {
    background: transparent;
}

.theme-toggle,
.theme-toggle * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.log-actions {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    align-items: center;
}

.log-actions .select-container {
    min-width: 120px;
}

/* Miuix风格按钮 */
.btn {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    min-height: 44px;
}

.btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(var(--accent-color-rgb, 13, 132, 255), 0.4);
    background: linear-gradient(135deg, var(--accent-hover) 0%, var(--accent-color) 100%);
}

.btn:hover::before {
    opacity: 1;
}

.btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px rgba(var(--accent-color-rgb, 13, 132, 255), 0.4);
}

.btn:focus {
    box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 13, 132, 255), 0.3), 0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
}

/* 按钮图标样式 */
.btn .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn .btn-icon svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
}

/* 主要按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #00c853 0%, #4caf50 100%);
    box-shadow: 0 4px 12px rgba(0, 200, 83, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #00e676 0%, #66bb6a 100%);
    box-shadow: 0 6px 20px rgba(0, 200, 83, 0.4);
}

.select {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
}

/* 自定义下拉菜单样式 */
.custom-select {
    position: relative;
    width: 100%;
    cursor: pointer;
    z-index: 10;
    /* 默认z-index */
}

/* 为语言选择器设置更高的z-index */
#languageContainer {
    z-index: 30;
}

/* 为日志等级选择器设置较低的z-index */
#logLevelContainer {
    z-index: 20;
}

.selected-option {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s;
}

/* 鼠标悬停时的样式 */
.selected-option:hover {
    border-color: var(--accent-color);
}

/* 打开状态时的样式 */
.custom-select.open .selected-option {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.selected-option::after {
    content: "▼";
    font-size: 10px;
    margin-left: 8px;
    transition: transform 0.3s;
    color: var(--secondary-text-color);
}

.custom-select.open .selected-option::after {
    transform: rotate(180deg);
    color: white;
}

.options-container {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-top: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 11;
    /* 默认比选择器高1 */
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    opacity: 0;
}

/* 为语言选择器的选项容器设置更高的z-index */
#languageOptions {
    z-index: 31;
}

/* 为日志等级选择器的选项容器设置较低的z-index */
#logLevelOptions {
    z-index: 21;
}

.custom-select.open .options-container {
    max-height: 200px;
    overflow: visible;
    /* 确保内容不会被截断 */
    opacity: 1;
}

.option {
    padding: 8px 12px;
    font-size: 14px;
    color: var(--text-color);
    transition: background-color 0.2s;
    border-bottom: 1px solid var(--border-color);
}

.option:last-child {
    border-bottom: none;
}

.option:hover {
    background-color: var(--border-color);
}

.option.selected {
    background-color: var(--accent-color);
    color: white;
}

/* Miuix风格数值选择器样式 */
.number-spinner {
    display: flex;
    align-items: center;
    border: 2px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    border-radius: 12px;
    overflow: hidden;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.number-spinner:hover {
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.15);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.spinner-btn {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    color: white;
    border: none;
    width: 44px;
    height: 44px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    -webkit-user-select: none;
    -webkit-tap-highlight-color: transparent;
    position: relative;
    overflow: hidden;
}

.spinner-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.spinner-btn:hover {
    background: linear-gradient(135deg, var(--accent-hover) 0%, var(--accent-color) 100%);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
}

.spinner-btn:hover::before {
    opacity: 1;
}

.spinner-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2px 6px rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
}

.spinner-btn:disabled {
    background: linear-gradient(135deg, var(--border-color) 0%, var(--secondary-text-color) 100%);
    cursor: not-allowed;
    opacity: 0.7;
    transform: none;
    box-shadow: none;
}

.spinner-btn:disabled::before {
    display: none;
}

.spinner-value {
    flex: 1;
    text-align: center;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    background: rgba(var(--accent-color-rgb, 13, 132, 255), 0.05);
    border-left: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.1);
    border-right: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.1);
    transition: all 0.3s ease;
}

/* 深色模式数字旋转器样式 */
[data-theme="dark"] .number-spinner {
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.15);
}

[data-theme="dark"] .number-spinner:hover {
    border-color: rgba(13, 132, 255, 0.25);
}

[data-theme="dark"] .spinner-value {
    background: rgba(13, 132, 255, 0.08);
    border-left-color: rgba(13, 132, 255, 0.2);
    border-right-color: rgba(13, 132, 255, 0.2);
}

/* Miuix风格日志内容 */
.log-content {
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    border: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    border-radius: 12px;
    padding: 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    max-height: 500px;
    overflow-y: auto;
    transition: all 0.3s ease-in-out;
    animation: slideIn 0.3s ease-out;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
    color: var(--text-color);
}

.log-content:hover {
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.15);
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 深色模式日志样式 */
[data-theme="dark"] .log-content {
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.15);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .log-content:hover {
    border-color: rgba(13, 132, 255, 0.25);
    box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.3);
}

.loading-text {
    text-align: center;
    color: var(--secondary-text-color);
    padding: 12px;
}

/* 页面容器样式 */
.page-container {
    position: relative;
    min-height: calc(100vh - 200px);
    overflow: visible;
    /* 确保弹出内容不会被截断 */
}

.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
    overflow: visible;
    /* 确保弹出内容不会被截断 */
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* 底部导航栏样式 */
.nav-bar {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    /* 改为顶部对齐 */
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 80px;
    /* 进一步增加底栏高度 */
    background-color: var(--card-background);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 90;
    border-top: 1px solid var(--border-color);
    padding-top: 10px;
    /* 顶部内边距，使内容整体上移 */
    -webkit-tap-highlight-color: transparent;
    /* 移除点击时的高亮效果 */
    user-select: none;
    /* 防止文本被选中 */
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    /* 从顶部开始布局 */
    width: 25%;
    height: 60px;
    /* 限制高度 */
    background: none;
    border: none;
    color: var(--secondary-text-color);
    cursor: pointer;
    transition: all 0.3s;
    padding: 0;
    position: relative;
    overflow: visible;
    /* 确保内容不被截断 */
    -webkit-tap-highlight-color: transparent;
    /* 移除点击时的高亮效果 */
    outline: none;
    /* 移除默认焦点轮廓 */
}

.nav-item.active {
    color: var(--accent-color);
}

.nav-item.active::before {
    content: "";
    position: absolute;
    top: 0;
    width: 70%;
    height: 42px;
    /* 增加高度以容纳文字和图标 */
    background-color: var(--accent-bg);
    border-radius: 18px;
    z-index: -1;
    transition: all 0.3s;
}

.nav-icon {
    font-size: 20px;
    margin-bottom: 4px;
    transition: transform 0.3s;
    margin-top: 0;
    /* 移除顶部边距 */
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateY(12px);
    /* 默认状态下图标向下移动更多，比选中状态低不少 */
}

.nav-icon svg {
    width: 20px;
    height: 20px;
    transition: transform 0.3s;
}

.nav-text {
    font-size: 12px;
    height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.3s;
    margin-top: 4px;
    /* 增加与图标的间距 */
}

.nav-item.active .nav-icon {
    transform: translateY(8px);
    /* 向下移动图标更多距离 */
}

.nav-item.active .nav-icon svg {
    transform: translateY(0px);
    /* 移除额外的向下移动 */
}

.nav-item.active .nav-text {
    height: 16px;
    opacity: 1;
    transform: translateY(14px);
    /* 选中状态文字向下移动更多 */
}

/* Miuix风格版权信息 */
#copyrightCard {
    margin-top: 24px;
    background: linear-gradient(135deg, rgba(var(--accent-color-rgb, 13, 132, 255), 0.05) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    border: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.1);
}

.copyright-content {
    text-align: center;
    padding: 20px !important;
    color: var(--secondary-text-color);
    font-size: 13px;
    line-height: 1.5;
    position: relative;
    z-index: 1;
}

.copyright-content p {
    margin: 0;
    font-weight: 500;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
}

/* 深色模式版权样式 */
[data-theme="dark"] #copyrightCard {
    background: linear-gradient(135deg, rgba(13, 132, 255, 0.08) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.2);
}

/* 配置编辑相关样式 */
.config-actions,
.margin-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
}

/* Miuix风格编辑和删除按钮 */
.edit-btn,
.delete-btn {
    background: transparent;
    border: 2px solid transparent;
    cursor: pointer;
    padding: 8px;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.edit-btn::before,
.delete-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.edit-btn svg,
.delete-btn svg,
.game-delete-btn svg {
    width: 18px;
    height: 18px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.edit-btn {
    color: var(--accent-color);
}

.edit-btn::before {
    background: linear-gradient(135deg, rgba(var(--accent-color-rgb, 13, 132, 255), 0.1) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.05) 100%);
}

.edit-btn:hover {
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(var(--accent-color-rgb, 13, 132, 255), 0.2);
}

.edit-btn:hover::before {
    opacity: 1;
}

.delete-btn {
    color: var(--error-color);
}

.delete-btn::before {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.1) 0%, rgba(244, 67, 54, 0.05) 100%);
}

.delete-btn:hover {
    border-color: rgba(231, 76, 60, 0.3);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.2);
}

.delete-btn:hover::before {
    opacity: 1;
}

.edit-btn:hover svg,
.delete-btn:hover svg,
.game-delete-btn:hover svg {
    transform: scale(1.15) rotate(5deg);
}

.edit-btn:active,
.delete-btn:active {
    transform: translateY(0) scale(0.95);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 深色模式编辑删除按钮 */
[data-theme="dark"] .edit-btn::before {
    background: linear-gradient(135deg, rgba(13, 132, 255, 0.15) 0%, rgba(13, 132, 255, 0.08) 100%);
}

[data-theme="dark"] .delete-btn::before {
    background: linear-gradient(135deg, rgba(231, 76, 60, 0.15) 0%, rgba(244, 67, 54, 0.08) 100%);
}

/* Miuix风格模态对话框 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    animation: modalFadeIn 0.3s ease-out;
}

.modal-content {
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    margin: 10% auto;
    padding: 32px;
    border-radius: 20px;
    border: 1px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 500px;
    position: relative;
    z-index: 2001;
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center top;
}

.modal-content::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at top right, rgba(var(--accent-color-rgb, 13, 132, 255), 0.03) 0%, transparent 70%);
    border-radius: 20px;
    pointer-events: none;
}

.close-modal {
    color: var(--secondary-text-color);
    position: absolute;
    top: 16px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    background: transparent;
}

.close-modal:hover {
    color: var(--text-color);
    background: rgba(var(--accent-color-rgb, 13, 132, 255), 0.1);
    transform: scale(1.1);
}

.modal h3 {
    margin: 0 0 24px 0;
    color: var(--text-color);
    font-size: 20px;
    font-weight: 600;
    padding-right: 40px;
    position: relative;
    z-index: 1;
}

/* 模态对话框动画 */
@keyframes modalFadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 深色模式模态对话框 */
[data-theme="dark"] .modal {
    background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .modal-content {
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.15);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .modal-content::before {
    background: radial-gradient(circle at top right, rgba(13, 132, 255, 0.08) 0%, transparent 70%);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

/* Miuix风格表单输入框 */
.form-group input,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    border-radius: 12px;
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    color: var(--text-color);
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.4);
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.05) 100%);
    box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb, 13, 132, 255), 0.2), 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.form-group input:hover,
.form-group select:hover {
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.15);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

/* 深色模式表单输入框 */
[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select {
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(13, 132, 255, 0.05) 100%);
    border-color: rgba(13, 132, 255, 0.15);
}

[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group select:focus {
    border-color: rgba(13, 132, 255, 0.5);
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(13, 132, 255, 0.08) 100%);
    box-shadow: 0 0 0 3px rgba(13, 132, 255, 0.3), 0 4px 12px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .form-group input:hover,
[data-theme="dark"] .form-group select:hover {
    border-color: rgba(13, 132, 255, 0.25);
}

.input-hint {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--secondary-text-color);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
}

/* 次要按钮样式 */
.btn-secondary {
    background: linear-gradient(135deg, var(--border-color) 0%, var(--secondary-text-color) 100%);
    color: var(--text-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-text-color) 0%, var(--border-color) 100%);
    color: var(--card-background);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 危险按钮样式 */
.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    margin-left: auto;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ec7063 0%, #e74c3c 100%);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* 设置页面选择器的网格标签按钮样式 */
.settings-tabs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    width: 100%;
    max-width: 400px;
}

/* Miuix风格设置标签按钮 */
.settings-tab-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 14px 10px;
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.02) 100%);
    border: 2px solid rgba(var(--accent-color-rgb, 13, 132, 255), 0.08);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--text-color);
    min-height: 70px;
    position: relative;
    overflow: hidden;
    font-size: 13px;
    line-height: 1.3;
    text-align: center;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.settings-tab-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.settings-tab-btn:hover {
    border-color: rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
    background: linear-gradient(135deg, var(--card-background) 0%, rgba(var(--accent-color-rgb, 13, 132, 255), 0.08) 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.settings-tab-btn:hover::before {
    opacity: 1;
}

.settings-tab-btn.active {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    border-color: var(--accent-color);
    color: white;
    box-shadow: 0 4px 16px rgba(var(--accent-color-rgb, 13, 132, 255), 0.3);
    transform: translateY(-1px);
}

.settings-tab-btn.active::before {
    opacity: 1;
}

.settings-tab-btn .tab-icon {
    font-size: 16px;
    margin-bottom: 4px;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.settings-tab-btn .tab-icon svg {
    width: 16px;
    height: 16px;
    transition: transform 0.3s ease;
}

.settings-tab-btn .tab-text {
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

.settings-tab-btn:hover .tab-icon {
    transform: scale(1.05);
}

.settings-tab-btn:hover .tab-icon svg {
    transform: scale(1.05);
}

.settings-tab-btn.active .tab-icon {
    animation: pulse 2s infinite;
}

/* 语言选择器特定样式 */
.language-tabs-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 360px;
}

/* 日志等级选择器特定样式 */
.log-level-tabs-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 300px;
}

/* Miuix风格的SuperSwitch组件 */
.miuix-super-switch {
    background: var(--card-background);
    border-radius: 14px;
    border: 1px solid var(--border-color);
    margin-bottom: 12px;
    overflow: hidden;
    transition: all 0.2s ease;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.miuix-super-switch:hover {
    background: color-mix(in srgb, var(--card-background) 92%, var(--accent-color));
    border-color: color-mix(in srgb, var(--border-color) 70%, var(--accent-color));
}

.miuix-super-switch:active {
    transform: scale(0.98);
    background: color-mix(in srgb, var(--card-background) 88%, var(--accent-color));
}

.miuix-super-switch-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    min-height: 64px;
}

.miuix-super-switch-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0;
    /* 允许文本截断 */
}

.miuix-super-switch-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    line-height: 1.3;
    word-wrap: break-word;
}

.miuix-super-switch-summary {
    font-size: 13px;
    color: var(--secondary-text-color);
    line-height: 1.4;
    opacity: 0.8;
    word-wrap: break-word;
}

/* Miuix风格的Switch组件 */
.miuix-switch {
    position: relative;
    width: 48px;
    height: 28px;
    flex-shrink: 0;
    margin-left: 16px;
}

.miuix-switch-input {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
    z-index: 2;
}

.miuix-switch-track {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: color-mix(in srgb, var(--text-color) 20%, transparent);
    border-radius: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.miuix-switch-thumb {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: #ffffff;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transform: translateX(0);
}

/* 深色模式下的thumb颜色调整 */
[data-theme="dark"] .miuix-switch-thumb {
    background: #f0f0f0;
}

/* Switch选中状态 */
.miuix-switch-input:checked+.miuix-switch-track {
    background: var(--accent-color);
}

.miuix-switch-input:checked+.miuix-switch-track .miuix-switch-thumb {
    transform: translateX(20px);
    background: #ffffff;
}

/* Switch悬停效果 */
.miuix-switch:hover .miuix-switch-track {
    box-shadow: 0 0 0 8px color-mix(in srgb, var(--accent-color) 12%, transparent);
}

.miuix-switch-input:checked:hover+.miuix-switch-track {
    background: color-mix(in srgb, var(--accent-color) 90%, #000000);
}

/* Switch按下效果 */
.miuix-switch-input:active+.miuix-switch-track .miuix-switch-thumb {
    width: 26px;
}

.miuix-switch-input:checked:active+.miuix-switch-track .miuix-switch-thumb {
    transform: translateX(18px);
}

/* Switch禁用状态 */
.miuix-switch-input:disabled+.miuix-switch-track {
    opacity: 0.5;
    cursor: not-allowed;
}

.miuix-switch-input:disabled+.miuix-switch-track .miuix-switch-thumb {
    background: color-mix(in srgb, var(--text-color) 40%, transparent);
}

.miuix-super-switch:has(.miuix-switch-input:disabled) {
    opacity: 0.6;
    cursor: not-allowed;
}

.miuix-super-switch:has(.miuix-switch-input:disabled):hover {
    transform: none;
    background: var(--card-background);
    border-color: var(--border-color);
}

/* 焦点状态 */
.miuix-switch-input:focus-visible+.miuix-switch-track {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* 为SuperSwitch添加焦点状态 */
.miuix-super-switch:focus-within {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .miuix-super-switch-content {
        padding: 14px 16px;
        min-height: 60px;
    }

    .miuix-super-switch-title {
        font-size: 15px;
    }

    .miuix-super-switch-summary {
        font-size: 12px;
    }

    .miuix-switch {
        margin-left: 12px;
    }
}

/* 动画增强 */
@media (prefers-reduced-motion: no-preference) {
    .miuix-super-switch {
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .miuix-switch-thumb {
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .miuix-switch-track {
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }
}

/* 为SuperSwitch添加点击波纹效果 */
.miuix-super-switch::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: color-mix(in srgb, var(--accent-color) 20%, transparent);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.miuix-super-switch:active::before {
    width: 100%;
    height: 100%;
}

/* 确保内容在波纹效果之上 */
.miuix-super-switch-content {
    position: relative;
    z-index: 2;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .miuix-super-switch {
        border-width: 2px;
    }

    .miuix-switch-track {
        border: 1px solid var(--text-color);
    }

    .miuix-switch-thumb {
        border: 1px solid var(--text-color);
    }
}

/* 深色模式下的特殊优化 */
[data-theme="dark"] .miuix-super-switch:hover {
    background: color-mix(in srgb, var(--card-background) 85%, var(--accent-color));
}

[data-theme="dark"] .miuix-super-switch:active {
    background: color-mix(in srgb, var(--card-background) 75%, var(--accent-color));
}

/* 为color-mix不支持的浏览器提供fallback */
@supports not (color: color-mix(in srgb, red, blue)) {
    .miuix-super-switch:hover {
        background: var(--accent-bg);
    }

    .miuix-switch-track {
        background: rgba(128, 128, 128, 0.3);
    }

    .miuix-switch-input:checked+.miuix-switch-track {
        background: var(--accent-color);
    }
}
