package com.mediatek.gpu.governor.ui.pages

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Icon
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.mediatek.gpu.governor.data.GpuConfig
import com.mediatek.gpu.governor.ui.components.AppIcons
import com.mediatek.gpu.governor.viewmodel.AppViewModel
import top.yukonga.miuix.kmp.basic.*

@Composable
fun ConfigPage(viewModel: AppViewModel) {
    val gpuConfigs by viewModel.gpuConfigs.collectAsState()
    val gamesList by viewModel.gamesList.collectAsState()
    val marginPercentage by viewModel.marginPercentage.collectAsState()

    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // GPU频率表卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    SmallTitle(text = "GPU频率表")
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 表头
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("频率(MHz)", modifier = Modifier.weight(1f))
                        Text("电压(uV)", modifier = Modifier.weight(1f))
                        Text("内存档位", modifier = Modifier.weight(1f))
                        Text("操作", modifier = Modifier.weight(1f))
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // 配置列表
                    gpuConfigs.forEach { config ->
                        ConfigItem(
                            config = config,
                            onEdit = { /* TODO: 实现编辑功能 */ },
                            onDelete = { /* TODO: 实现删除功能 */ }
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                // 添加默认配置
                                viewModel.addGpuConfig(GpuConfig(500000, 60000, 999))
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("添加配置")
                        }

                        Button(
                            onClick = { viewModel.saveGpuConfigs() },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("保存配置")
                        }
                    }
                }
            }
        }

        item {
            // GPU余量配置卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    SmallTitle(text = "GPU频率计算余量")
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = AppIcons.Tune,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("余量百分比 (%):")
                        }

                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Button(
                                onClick = {
                                    if (marginPercentage > 0) {
                                        viewModel.setMarginPercentage(marginPercentage - 5)
                                    }
                                }
                            ) {
                                Text("-")
                            }

                            Text(
                                text = marginPercentage.toString(),
                                modifier = Modifier.padding(horizontal = 16.dp),
                                style = androidx.compose.material3.MaterialTheme.typography.titleMedium
                            )

                            Button(
                                onClick = {
                                    if (marginPercentage < 100) {
                                        viewModel.setMarginPercentage(marginPercentage + 5)
                                    }
                                }
                            ) {
                                Text("+")
                            }
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "余量越大，GPU升频越积极，性能越充裕",
                        style = androidx.compose.material3.MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "游戏模式下会自动增加10%的余量",
                        style = androidx.compose.material3.MaterialTheme.typography.bodySmall
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Button(
                        onClick = { viewModel.saveMarginSettings() },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("保存余量设置")
                    }
                }
            }
        }

        item {
            // 游戏列表卡片
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    SmallTitle(text = "游戏列表")
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    gamesList.forEach { game ->
                        GameItem(
                            packageName = game,
                            onRemove = { viewModel.removeGame(game) }
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                // 添加示例游戏包名
                                viewModel.addGame("com.example.game")
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("添加游戏")
                        }

                        Button(
                            onClick = { viewModel.saveGamesList() },
                            modifier = Modifier.weight(1f)
                        ) {
                            Text("保存列表")
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ConfigItem(
    config: GpuConfig,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${config.frequency / 1000} MHz",
                    style = androidx.compose.material3.MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "频率",
                    style = androidx.compose.material3.MaterialTheme.typography.bodySmall,
                    color = androidx.compose.material3.MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "${config.voltage} uV",
                    style = androidx.compose.material3.MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "电压",
                    style = androidx.compose.material3.MaterialTheme.typography.bodySmall,
                    color = androidx.compose.material3.MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = if (config.ddrLevel == 999) "不调整" else "档位 ${config.ddrLevel}",
                    style = androidx.compose.material3.MaterialTheme.typography.bodyMedium
                )
                Text(
                    text = "内存",
                    style = androidx.compose.material3.MaterialTheme.typography.bodySmall,
                    color = androidx.compose.material3.MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Button(onClick = onEdit) {
                Icon(
                    imageVector = AppIcons.Edit,
                    contentDescription = "编辑"
                )
            }
        }
    }
}

@Composable
private fun GameItem(
    packageName: String,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                modifier = Modifier.weight(1f),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = AppIcons.SportsEsports,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                    tint = androidx.compose.material3.MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = packageName,
                    style = androidx.compose.material3.MaterialTheme.typography.bodyMedium
                )
            }

            Button(onClick = onRemove) {
                Icon(
                    imageVector = AppIcons.Delete,
                    contentDescription = "删除"
                )
            }
        }
    }
}
